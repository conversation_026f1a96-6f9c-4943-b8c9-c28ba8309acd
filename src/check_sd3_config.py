#!/usr/bin/env python3
"""
Check SD3.5 model configuration to understand expected dimensions
"""

import torch
from diffusers import SD3Transformer2DModel
from transformers import CLIPTextModel, CLIPTextModelWithProjection, T5EncoderModel

def check_sd3_config():
    """Check SD3.5 model configuration"""
    
    print("🔍 Checking SD3.5 model configuration...")
    
    model_id = "stabilityai/stable-diffusion-3.5-large"
    
    try:
        # Load transformer and check its configuration
        print("\n📋 Loading transformer configuration...")
        transformer = SD3Transformer2DModel.from_pretrained(
            model_id, subfolder="transformer", torch_dtype=torch.float16
        )
        
        print(f"Transformer config:")
        config = transformer.config
        print(f"  - attention_head_dim: {getattr(config, 'attention_head_dim', 'N/A')}")
        print(f"  - num_attention_heads: {getattr(config, 'num_attention_heads', 'N/A')}")
        print(f"  - in_channels: {getattr(config, 'in_channels', 'N/A')}")
        print(f"  - out_channels: {getattr(config, 'out_channels', 'N/A')}")
        print(f"  - pooled_projection_dim: {getattr(config, 'pooled_projection_dim', 'N/A')}")
        print(f"  - text_encoder_projection_dim: {getattr(config, 'text_encoder_projection_dim', 'N/A')}")
        print(f"  - joint_attention_dim: {getattr(config, 'joint_attention_dim', 'N/A')}")
        
        # Check text encoders
        print("\n🧠 Checking text encoder configurations...")
        
        # CLIP 1
        text_encoder_1 = CLIPTextModel.from_pretrained(
            model_id, subfolder="text_encoder"
        )
        print(f"CLIP 1 config:")
        print(f"  - hidden_size: {text_encoder_1.config.hidden_size}")
        print(f"  - projection_dim: {getattr(text_encoder_1.config, 'projection_dim', 'N/A')}")
        
        # CLIP 2
        text_encoder_2 = CLIPTextModelWithProjection.from_pretrained(
            model_id, subfolder="text_encoder_2"
        )
        print(f"CLIP 2 config:")
        print(f"  - hidden_size: {text_encoder_2.config.hidden_size}")
        print(f"  - projection_dim: {text_encoder_2.config.projection_dim}")
        
        # T5
        text_encoder_3 = T5EncoderModel.from_pretrained(
            model_id, subfolder="text_encoder_3"
        )
        print(f"T5 config:")
        print(f"  - d_model: {text_encoder_3.config.d_model}")
        
        # Check what the transformer expects for pooled projections
        print(f"\n🎯 Expected dimensions:")
        pooled_proj_dim = getattr(config, 'pooled_projection_dim', None)
        if pooled_proj_dim:
            print(f"  - Pooled projection dim: {pooled_proj_dim}")
        else:
            print("  - Pooled projection dim: Not specified in config")
            
        # Try to infer from the time_text_embed module
        if hasattr(transformer, 'time_text_embed'):
            time_text_embed = transformer.time_text_embed
            if hasattr(time_text_embed, 'text_embedder'):
                text_embedder = time_text_embed.text_embedder
                if hasattr(text_embedder, 'linear_1'):
                    linear_1 = text_embedder.linear_1
                    expected_input_dim = linear_1.in_features
                    print(f"  - Text embedder expects input dim: {expected_input_dim}")
                    
                    # This is what we need to match!
                    print(f"\n💡 Solution: Pooled projections should have dimension {expected_input_dim}")
                    
                    # Calculate what we need
                    clip1_dim = text_encoder_1.config.hidden_size  # 768
                    clip2_dim = text_encoder_2.config.projection_dim  # 1280
                    current_combined = clip1_dim + clip2_dim
                    
                    print(f"  - CLIP 1 hidden size: {clip1_dim}")
                    print(f"  - CLIP 2 projection dim: {clip2_dim}")
                    print(f"  - Combined: {current_combined}")
                    print(f"  - Expected: {expected_input_dim}")
                    
                    if current_combined != expected_input_dim:
                        print(f"  - Need to adjust by: {expected_input_dim - current_combined}")
        
        print(f"\n📊 Full transformer config:")
        for key, value in config.__dict__.items():
            if not key.startswith('_'):
                print(f"  - {key}: {value}")
                
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_sd3_config()
