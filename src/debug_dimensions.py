#!/usr/bin/env python3
"""
Debug script to check tensor dimensions in SD3.5 training
"""

import torch
from transformers import CLIPTextModel, CLIPTokenizer, CLIPTextModelWithProjection, T5EncoderModel, T5TokenizerFast
from diffusers import SD3Transformer2DModel, AutoencoderKL

def debug_sd3_dimensions():
    """Debug SD3.5 tensor dimensions to identify the matrix multiplication issue"""
    
    print("🔍 Debugging SD3.5 tensor dimensions...")
    
    model_id = "stabilityai/stable-diffusion-3.5-large"
    device = "cuda" if torch.cuda.is_available() else "cpu"
    dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    
    print(f"Using device: {device}, dtype: {dtype}")
    
    try:
        # Load tokenizers
        print("\n📝 Loading tokenizers...")
        tokenizer = CLIPTokenizer.from_pretrained(model_id, subfolder="tokenizer")
        tokenizer_2 = CLIPTokenizer.from_pretrained(model_id, subfolder="tokenizer_2")
        tokenizer_3 = T5TokenizerFast.from_pretrained(model_id, subfolder="tokenizer_3")
        
        # Load text encoders
        print("🧠 Loading text encoders...")
        text_encoder = CLIPTextModel.from_pretrained(
            model_id, subfolder="text_encoder", torch_dtype=dtype
        ).to(device)
        text_encoder_2 = CLIPTextModelWithProjection.from_pretrained(
            model_id, subfolder="text_encoder_2", torch_dtype=dtype
        ).to(device)
        text_encoder_3 = T5EncoderModel.from_pretrained(
            model_id, subfolder="text_encoder_3", torch_dtype=dtype
        ).to(device)
        
        # Load transformer
        print("🔄 Loading transformer...")
        transformer = SD3Transformer2DModel.from_pretrained(
            model_id, subfolder="transformer", torch_dtype=dtype
        ).to(device)
        
        # Test with sample text
        test_prompt = "a photo of character1, standing in a field"
        batch_size = 2
        
        print(f"\n🧪 Testing with prompt: '{test_prompt}'")
        print(f"Batch size: {batch_size}")
        
        # Tokenize
        print("\n📊 Tokenizing...")
        text_inputs = tokenizer(
            [test_prompt] * batch_size,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )
        text_inputs_2 = tokenizer_2(
            [test_prompt] * batch_size,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )
        text_inputs_3 = tokenizer_3(
            [test_prompt] * batch_size,
            padding="max_length",
            max_length=256,
            truncation=True,
            return_tensors="pt"
        )
        
        # Move to device
        input_ids = text_inputs.input_ids.to(device)
        input_ids_2 = text_inputs_2.input_ids.to(device)
        input_ids_3 = text_inputs_3.input_ids.to(device)
        attention_mask = text_inputs.attention_mask.to(device)
        attention_mask_2 = text_inputs_2.attention_mask.to(device)
        attention_mask_3 = text_inputs_3.attention_mask.to(device)
        
        print(f"Input IDs shapes: {input_ids.shape}, {input_ids_2.shape}, {input_ids_3.shape}")
        
        # Encode text
        print("\n🔤 Encoding text...")
        with torch.no_grad():
            text_encoder_output = text_encoder(
                input_ids=input_ids,
                attention_mask=attention_mask,
                return_dict=True
            )
            prompt_embeds_1 = text_encoder_output.last_hidden_state
            
            text_encoder_2_output = text_encoder_2(
                input_ids=input_ids_2,
                attention_mask=attention_mask_2,
                return_dict=True
            )
            prompt_embeds_2 = text_encoder_2_output.last_hidden_state
            pooled_prompt_embeds_2 = text_encoder_2_output.text_embeds
            
            text_encoder_3_output = text_encoder_3(
                input_ids=input_ids_3,
                attention_mask=attention_mask_3,
                return_dict=True
            )
            prompt_embeds_3 = text_encoder_3_output.last_hidden_state
        
        print(f"Text encoder outputs:")
        print(f"  CLIP 1: {prompt_embeds_1.shape}")
        print(f"  CLIP 2: {prompt_embeds_2.shape}")
        print(f"  T5: {prompt_embeds_3.shape}")
        print(f"  Pooled: {pooled_prompt_embeds_2.shape}")
        
        # Test different embedding combinations
        print("\n🔗 Testing embedding combinations...")
        
        # Method 1: Concatenate CLIP along feature dim, then concat with T5 along sequence dim
        clip_combined = torch.cat([prompt_embeds_1, prompt_embeds_2], dim=-1)
        print(f"CLIP combined shape: {clip_combined.shape}")
        
        # Pad to match sequence lengths
        clip_seq_len = clip_combined.shape[1]
        t5_seq_len = prompt_embeds_3.shape[1]
        
        if clip_seq_len < t5_seq_len:
            padding = torch.zeros(
                batch_size, t5_seq_len - clip_seq_len, clip_combined.shape[2],
                device=clip_combined.device, dtype=clip_combined.dtype
            )
            clip_padded = torch.cat([clip_combined, padding], dim=1)
        else:
            clip_padded = clip_combined
            
        print(f"CLIP padded shape: {clip_padded.shape}")
        
        # Method 2: Pad feature dimensions to match
        clip_feat_dim = clip_padded.shape[-1]
        t5_feat_dim = prompt_embeds_3.shape[-1]
        
        if clip_feat_dim != t5_feat_dim:
            if clip_feat_dim < t5_feat_dim:
                feat_padding = torch.zeros(
                    batch_size, clip_padded.shape[1], t5_feat_dim - clip_feat_dim,
                    device=clip_padded.device, dtype=clip_padded.dtype
                )
                clip_final = torch.cat([clip_padded, feat_padding], dim=-1)
                t5_final = prompt_embeds_3
            else:
                feat_padding = torch.zeros(
                    batch_size, prompt_embeds_3.shape[1], clip_feat_dim - t5_feat_dim,
                    device=prompt_embeds_3.device, dtype=prompt_embeds_3.dtype
                )
                t5_final = torch.cat([prompt_embeds_3, feat_padding], dim=-1)
                clip_final = clip_padded
        else:
            clip_final = clip_padded
            t5_final = prompt_embeds_3
            
        print(f"Final shapes - CLIP: {clip_final.shape}, T5: {t5_final.shape}")
        
        # Concatenate along sequence dimension
        encoder_hidden_states = torch.cat([clip_final, t5_final], dim=1)
        print(f"Final encoder_hidden_states shape: {encoder_hidden_states.shape}")
        
        # Test transformer input
        print("\n🤖 Testing transformer input...")
        
        # Create dummy latents
        latents = torch.randn(batch_size, 16, 64, 64, dtype=dtype, device=device)
        timesteps = torch.rand(batch_size, dtype=dtype, device=device)
        
        print(f"Latents shape: {latents.shape}")
        print(f"Timesteps shape: {timesteps.shape}")
        print(f"Encoder hidden states shape: {encoder_hidden_states.shape}")
        print(f"Pooled projections shape: {pooled_prompt_embeds_2.shape}")
        
        # Try transformer forward pass
        try:
            with torch.no_grad():
                output = transformer(
                    hidden_states=latents,
                    timestep=timesteps,
                    encoder_hidden_states=encoder_hidden_states,
                    pooled_projections=pooled_prompt_embeds_2,
                    return_dict=False
                )
            print(f"✅ Transformer forward pass successful!")
            print(f"Output shape: {output[0].shape}")
            
        except Exception as e:
            print(f"❌ Transformer forward pass failed: {e}")
            
            # Try with CLIP only
            print("\n🔄 Trying with CLIP embeddings only...")
            try:
                with torch.no_grad():
                    output = transformer(
                        hidden_states=latents,
                        timestep=timesteps,
                        encoder_hidden_states=clip_combined,
                        pooled_projections=pooled_prompt_embeds_2,
                        return_dict=False
                    )
                print(f"✅ CLIP-only forward pass successful!")
                print(f"Output shape: {output[0].shape}")
                print("💡 Recommendation: Use --clip_only flag to avoid dimension issues")
                
            except Exception as e2:
                print(f"❌ CLIP-only forward pass also failed: {e2}")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_sd3_dimensions()
